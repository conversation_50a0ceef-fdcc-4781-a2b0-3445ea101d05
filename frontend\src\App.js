import React, { useState } from "react";
import "./App.css";
import axios from "axios";

const BACKEND_URL = process.env.REACT_APP_BACKEND_URL || "http://localhost:8000";
const API = `${BACKEND_URL}/api`;

function App() {
  const [file, setFile] = useState(null);
  const [isUploading, setIsUploading] = useState(false);
  const [document, setDocument] = useState(null);
  const [mcqs, setMcqs] = useState([]);
  const [flashcards, setFlashcards] = useState([]);
  const [chatMessages, setChatMessages] = useState([]);
  const [newMessage, setNewMessage] = useState("");
  const [isChatting, setIsChatting] = useState(false);
  const [currentTab, setCurrentTab] = useState("upload");

  const handleFileUpload = async () => {
    if (!file) {
      alert("Please select a PDF file first.");
      return;
    }

    setIsUploading(true);
    const formData = new FormData();
    formData.append("file", file);

    try {
      const response = await axios.post(`${API}/upload`, formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });

      setDocument(response.data);
      setMcqs(response.data.mcqs || []);
      setFlashcards(response.data.flashcards || []);
      setChatMessages([]);
      setCurrentTab("study");
    } catch (error) {
      console.error("Upload error:", error);
      alert("Error uploading file. Please try again.");
    } finally {
      setIsUploading(false);
    }
  };

  const sendMessage = async () => {
    if (!newMessage.trim() || !document) return;

    setIsChatting(true);
    const userMessage = newMessage;
    setNewMessage("");

    setChatMessages(prev => [...prev, { type: "user", message: userMessage }]);

    try {
      const response = await axios.post(`${API}/chat`, {
        document_id: document.document_id,
        message: userMessage,
      });

      setChatMessages(prev => [...prev, { type: "ai", message: response.data.response }]);
    } catch (error) {
      console.error("Chat error:", error);
      setChatMessages(prev => [...prev, { type: "ai", message: "Sorry, I encountered an error. Please try again." }]);
    } finally {
      setIsChatting(false);
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  return (
    <div style={{ padding: "20px", fontFamily: "Arial, sans-serif" }}>
      <h1 style={{ textAlign: "center", color: "#4F46E5" }}>StudyGenie</h1>

      {!document ? (
        <div style={{ maxWidth: "500px", margin: "0 auto", textAlign: "center" }}>
          <h2>Upload a PDF to get started</h2>
          <div style={{ margin: "20px 0" }}>
            <input
              type="file"
              accept=".pdf"
              onChange={(e) => setFile(e.target.files[0])}
              style={{ margin: "10px 0" }}
            />
            {file && <p>Selected: {file.name}</p>}
          </div>
          <button
            onClick={handleFileUpload}
            disabled={!file || isUploading}
            style={{
              padding: "10px 20px",
              backgroundColor: "#4F46E5",
              color: "white",
              border: "none",
              borderRadius: "5px",
              cursor: file && !isUploading ? "pointer" : "not-allowed",
              opacity: file && !isUploading ? 1 : 0.5
            }}
          >
            {isUploading ? "Processing..." : "Upload & Generate Study Materials"}
          </button>
        </div>
      ) : (
        <div>
          <div style={{ textAlign: "center", marginBottom: "20px" }}>
            <h2>Study materials for: {document.filename}</h2>
            <button
              onClick={() => window.location.reload()}
              style={{
                padding: "5px 10px",
                backgroundColor: "#6B7280",
                color: "white",
                border: "none",
                borderRadius: "3px",
                cursor: "pointer"
              }}
            >
              Upload New Document
            </button>
          </div>

          <div style={{ display: "flex", justifyContent: "center", marginBottom: "20px" }}>
            <button
              onClick={() => setCurrentTab("mcqs")}
              style={{
                padding: "10px 20px",
                backgroundColor: currentTab === "mcqs" ? "#4F46E5" : "#E5E7EB",
                color: currentTab === "mcqs" ? "white" : "black",
                border: "none",
                borderRadius: "5px 0 0 5px",
                cursor: "pointer"
              }}
            >
              Quiz ({mcqs.length})
            </button>
            <button
              onClick={() => setCurrentTab("flashcards")}
              style={{
                padding: "10px 20px",
                backgroundColor: currentTab === "flashcards" ? "#4F46E5" : "#E5E7EB",
                color: currentTab === "flashcards" ? "white" : "black",
                border: "none",
                cursor: "pointer"
              }}
            >
              Flashcards ({flashcards.length})
            </button>
            <button
              onClick={() => setCurrentTab("chat")}
              style={{
                padding: "10px 20px",
                backgroundColor: currentTab === "chat" ? "#4F46E5" : "#E5E7EB",
                color: currentTab === "chat" ? "white" : "black",
                border: "none",
                borderRadius: "0 5px 5px 0",
                cursor: "pointer"
              }}
            >
              AI Chat
            </button>
          </div>

          <div style={{ maxWidth: "800px", margin: "0 auto" }}>
            {currentTab === "mcqs" && (
              <div style={{ backgroundColor: "#F9FAFB", padding: "20px", borderRadius: "8px" }}>
                <h3>Quiz Questions</h3>
                {mcqs.length > 0 ? (
                  mcqs.map((mcq, index) => (
                    <div key={index} style={{ marginBottom: "20px", padding: "15px", backgroundColor: "white", borderRadius: "5px" }}>
                      <h4>{mcq.question}</h4>
                      <ul style={{ listStyle: "none", padding: 0 }}>
                        {mcq.options.map((option, optIndex) => (
                          <li key={optIndex} style={{
                            padding: "5px",
                            backgroundColor: optIndex === mcq.correct_answer ? "#D1FAE5" : "#F3F4F6",
                            margin: "5px 0",
                            borderRadius: "3px"
                          }}>
                            {optIndex === mcq.correct_answer ? "✓ " : ""}{option}
                          </li>
                        ))}
                      </ul>
                      <p style={{ fontSize: "14px", color: "#6B7280", marginTop: "10px" }}>
                        <strong>Explanation:</strong> {mcq.explanation}
                      </p>
                    </div>
                  ))
                ) : (
                  <p>No quiz questions available</p>
                )}
              </div>
            )}

            {currentTab === "flashcards" && (
              <div style={{ backgroundColor: "#F9FAFB", padding: "20px", borderRadius: "8px" }}>
                <h3>Flashcards</h3>
                {flashcards.length > 0 ? (
                  flashcards.map((card, index) => (
                    <div key={index} style={{ marginBottom: "15px", padding: "15px", backgroundColor: "white", borderRadius: "5px" }}>
                      <div style={{ fontWeight: "bold", marginBottom: "10px" }}>Q: {card.front}</div>
                      <div style={{ color: "#4F46E5" }}>A: {card.back}</div>
                    </div>
                  ))
                ) : (
                  <p>No flashcards available</p>
                )}
              </div>
            )}

            {currentTab === "chat" && (
              <div style={{ backgroundColor: "#F9FAFB", padding: "20px", borderRadius: "8px" }}>
                <h3>AI Chat</h3>
                <div style={{
                  height: "300px",
                  overflowY: "auto",
                  border: "1px solid #D1D5DB",
                  padding: "10px",
                  backgroundColor: "white",
                  borderRadius: "5px",
                  marginBottom: "10px"
                }}>
                  {chatMessages.length === 0 ? (
                    <p style={{ textAlign: "center", color: "#6B7280" }}>Start a conversation! Ask me anything about your document.</p>
                  ) : (
                    chatMessages.map((msg, index) => (
                      <div key={index} style={{
                        marginBottom: "10px",
                        textAlign: msg.type === "user" ? "right" : "left"
                      }}>
                        <div style={{
                          display: "inline-block",
                          padding: "8px 12px",
                          borderRadius: "10px",
                          backgroundColor: msg.type === "user" ? "#4F46E5" : "#E5E7EB",
                          color: msg.type === "user" ? "white" : "black",
                          maxWidth: "70%"
                        }}>
                          {msg.message}
                        </div>
                      </div>
                    ))
                  )}
                </div>
                <div style={{ display: "flex", gap: "10px" }}>
                  <input
                    type="text"
                    value={newMessage}
                    onChange={(e) => setNewMessage(e.target.value)}
                    onKeyPress={handleKeyPress}
                    placeholder="Ask a question about your document..."
                    disabled={isChatting}
                    style={{
                      flex: 1,
                      padding: "8px 12px",
                      border: "1px solid #D1D5DB",
                      borderRadius: "5px"
                    }}
                  />
                  <button
                    onClick={sendMessage}
                    disabled={!newMessage.trim() || isChatting}
                    style={{
                      padding: "8px 16px",
                      backgroundColor: "#4F46E5",
                      color: "white",
                      border: "none",
                      borderRadius: "5px",
                      cursor: !newMessage.trim() || isChatting ? "not-allowed" : "pointer",
                      opacity: !newMessage.trim() || isChatting ? 0.5 : 1
                    }}
                  >
                    {isChatting ? "..." : "Send"}
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

export default App;